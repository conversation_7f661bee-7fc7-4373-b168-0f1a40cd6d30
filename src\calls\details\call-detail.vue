<template>
  <div>
    <MountingPortal mountTo="#adapter-paccs">
      <!--      SUCCESS!!! PACCS have been inserted here.-->
      <div v-if="false">
        <div>PACCS</div>
        <div>
          callDetailController.state.showPaccs
          {{ callDetailController.state.showPaccs }}
        </div>
        <div>
          callDetailController.state.paccsReady
          {{ callDetailController.state.isPaccsReady }}
        </div>
        <div>User has PACCS Permission: {{ hasPermissionPaccs }}</div>
        <div>
          Consultation Started: {{ callDetailController.state.startedConsult }}
        </div>
        callDetailController.state.callDetail
        {{ callDetailController.state.callDetail }}
      </div>

      <div v-if="showPaccs && !isPaccsReady">
        <LoadingSpinnerLarge style="height: 100px;"></LoadingSpinnerLarge>
        <span style="font-weight: 600;font-size: 1rem;">Loading Paccs...</span>
      </div>

      <PaccsForm
        :paccs-form-data="callDetailController.state.paccsFormData"
        v-if="showPaccs && isPaccsReady"
      />
    </MountingPortal>

    <MountingPortal mountTo="#adapter-consults">
      <!--      SUCCESS!!! Consults have been inserted here.-->
      <ConsultsSection
        :consults="callDetailController.state.callDetail.Consults"
      ></ConsultsSection>
    </MountingPortal>

    <MountingPortal mountTo="#carehome-select">
      <!--      CAREHOME_SELECT: {{userPermissions.CAREHOME_SELECT ? true : false}}-->
      <CarehomeSelectOncall
        :selection-allowed="userPermissions.CAREHOME_SELECT ? true : false"
        :care-home="callDetailController.state.callDetail.CareHome"
        :service="callDetailController.state.callDetail.Service"
        :user-permissions="userPermissions"
        :is-new-call="isNewCall"
        v-on:selected="callDetailController.careHomeSelected"
      />
    </MountingPortal>

    <!--isLocalDevServer-->
    <div v-if="configStoreState.isLocalDevServer" class="dat-local-dev-server">
      Local Server Only
      <input id="cleoWhat3Words" />
      <button v-on:click="callDetailController.state.address.showModal = true">
        What3Words
      </button>
      <div id="cleo_content">
        This section represents the "cleo_content" section of call. On new call,
        dynamically add fields here.
      </div>

      CallTelNo_R:
      <span v-text="callDetailController.state.callDetail.CallTelNo_R"></span>
      SESUI_TELEPHPONE: userPermissions: {{ userPermissions.SESUI_TELEPHPONE }}

      <div class="ic24-flex-row ic24-flex-gap">
        Service:
        <select v-model="callDetailController.state.callDetail.Service">
          <option :value="{ id: 1, name: 'FCMS 111', serviceType: '111' }"
            >FCMS</option
          >
          <option :value="{ id: 2, name: 'BrisDoc 111', serviceType: '111' }"
            >BrisDoc</option
          >
          <option :value="{ id: 99, name: 'CAS', serviceType: 'CAS' }"
            >CAS</option
          >
          <option :value="{ id: 9, name: 'OOH', serviceType: 'OOH' }"
            >OOH</option
          >
        </select>
      </div>

      <div class="ic24-flex-row ic24-flex-gap">
        Classification (from Adapter):
        <select v-model="callDetailController.state.callDetail.Classification">
          <option
            v-for="classn in getClassifications"
            :key="classn.Id"
            :value="classn"
            v-text="classn.Description"
          ></option>
        </select>
      </div>

      <div class="ic24-flex-row ic24-flex-gap">
        CallNo:
        <input v-model="callDetailController.state.callDetail.CallNo" />
      </div>

      <div class="ic24-flex-row ic24-flex-gap">
        Classification:
        <select v-model="callDetailController.state.callDetail.Classification">
          <option
            v-for="classn in getClassifications"
            :key="classn.Id"
            :value="classn"
            v-text="classn.Description"
          ></option>
        </select>
      </div>

      <div class="ic24-flex-column">
        <div class="ic24-flex-row ic24-flex-gap">
          <span
            >CleoClientService (MENTAL_HEALTH, OUT_OF_HOURS_PROFESSIONAL_LINE,
            PAEDIATRICS, etc.):</span
          >
          <input
            v-model="callDetailController.state.callDetail.cleoClientService"
          />
          <select
            v-model="callDetailController.state.callDetail.cleoClientService"
          >
            <option value=""></option>
            <option value="MENTAL_HEALTH">MENTAL_HEALTH</option>
            <option value="OUT_OF_HOURS_PROFESSIONAL_LINE"
              >OUT_OF_HOURS_PROFESSIONAL_LINE</option
            >
            <option value="PAEDIATRICS">PAEDIATRICS</option>
            <option value="FRAILTY">FRAILTY</option>
            <option value="ADULTS">ADULTS</option>
            <option value="OTHER">OTHER</option>
          </select>
        </div>
      </div>
    </div>
    <!--/isLocalDevServer-->

    <MountingPortal mountTo="#adapter--sesui-call-form">
      <SesuiCallform
        v-if="userPermissions.SESUI_TELEPHPONE"
        v-model="callDetailController.state.callDetail.CallTelNo_R"
        :case-number="callDetailController.state.callDetail.CallNo"
        :user-permissions="userPermissions"
        v-on:onChanged="callDetailController.onTelephoneChanged"
      />
    </MountingPortal>

    <CleoModal
      header-message="Location Finder"
      v-if="callDetailController.state.address.showModal"
    >
      <div slot="body">
        <What3WordsCleo
          :what3words-controller-state="what3wordsControllerState"
          :call-detail="callDetailController.state.callDetail"
          :user-permissions="userPermissions"
          v-on:close="callDetailController.state.address.showModal = false"
          v-on:confirmed="what3wordsMapConfirmed"
        />
      </div>
      <div slot="buttons"></div>
    </CleoModal>

    <MountingPortal
      mountTo="#adapter--follow-up"
      name="adapter--follow-up-destination"
    >
      <div
        class="ic24-flex-row ic24-flex-gap-large ic24-justify-fle-row-vert-center"
      >
        <Ic24Button
          v-if="canShowFollowUp"
          title="Follow Up"
          @click="callDetailController.state.showFollowUp = true"
        ></Ic24Button>
        <span
          v-text="callDetailController.getFollowUpInputStateDisplayText.value"
        ></span>
      </div>
    </MountingPortal>

    <CleoModal
      header-message="Follow Up"
      v-if="callDetailController.state.showFollowUp"
      css-dialog-style="  background: rgb(242, 245, 247);"
      :remove-body-scroll="true"
    >
      <div slot="body" style="padding: 5px">
        <FollowUp
          v-if="callDetailController.state.showFollowUp"
          :call-detail="callDetailController.state.callDetail"
          @input="callDetailController.setFollowUpInputState"
        />
      </div>
      <div slot="buttons"></div>
    </CleoModal>

    <CleoModal
      :header-message="
        callDetailController.state.completeCase.process === 'COMPLETE_PROCESS'
          ? 'End Assessment'
          : 'Save & Return'
      "
      v-if="callDetailController.state.completeCase.show"
      css-dialog-style="  background: rgb(242, 245, 247);"
      :remove-body-scroll="true"
    >
      <div slot="body" style="padding: 5px">
        <CompleteForm
          :case-config="caseConfig"
          :call-detail="callDetailController.state.callDetail"
          :call-detail-state="callDetailController.state"
          :user-permissions="userPermissions"
          :complete-process="callDetailController.state.completeCase.process"
          @cancelProcess="cancelCompleteProcess"
          @saveAndReturnToQueue="saveAndReturnToQueue"
          @furtherActionRequired="furtherActionRequired"
          @processComplete="processComplete"
        />
      </div>
      <div slot="buttons"></div>
    </CleoModal>

    <!-- </CleoModal> -->
  </div>
</template>

<script lang="ts">
import {
  defineComponent,
  watch,
  SetupContext,
  computed,
  PropType,
  onMounted,
  onBeforeUnmount,
} from "@vue/composition-api";
import { ICallDetail, ICaseConfig } from "@/calls/details/call-details-models";
import { CommonService } from "@/common/common-service";
import * as CallDetailService from "@/calls/details/call-detail-service";
import { MountingPortal } from "portal-vue";
import ConsultsSection from "@/consults/consults-section.vue";
import PaccsForm from "@/paccs/paccs-form.vue";
import { appStore } from "@/store/store";
import { IPaccsStoreState, PACCS_STORE_CONST } from "@/paccs/paccs-store";
import LoadingSpinnerLarge from "@/common/ui/loading-spinner-large.vue";
import CleoModal from "@/common/ui/modal/cleo-modal.vue";
import {
  IWhat3wordsControllerState,
  IWhat3WordsMapResponse,
} from "@/what3words/what3words-cleo-models";
import { What3wordsCleoService } from "@/what3words/what3words-cleo-service";
import What3WordsCleo from "@/what3words/What3WordsCleo.vue";
import {
  CONFIG_STORE_CONST,
  IConfigStoreState,
} from "@/common/config/config-store";
import { IAdapterAction } from "@/app-models";
import {
  CleoPermissionsForRole,
  ICleoPermission,
} from "@/permissions/permission-models";
import { loggerInstance } from "@/common/Logger";
import CarehomeSelectOncall from "@/carehome/carehome-select-oncall.vue";
import { useCallDetailController } from "@/calls/details/useCallDetailController";
import SesuiCallform from "@/sesui/callform/sesui-callform.vue";
import {
  IPermissionStoreState,
  PERMISSION_STORE_CONST,
} from "@/permissions/permisssion-store";
import CompleteForm from "./complete/CompleteForm.vue";
import { ICompleteControllerState } from "@/calls/details/complete/complete-models";
import FollowUp from "@/calls/details/complete/components/followup/ui/FollowUp.vue";
import {
  IKeywordsStoreState,
  KEYWORD_STORE_STORE_CONST,
} from "@/keywords/keywords-store";
import { IClassification } from "@/keywords/keywords-models";
import Ic24Button from "@/common/ui/buttons/ic24-button.vue";

const commonService: CommonService = new CommonService();

export default defineComponent({
  name: "call-detail",
  components: {
    Ic24Button,
    FollowUp,
    SesuiCallform,
    CarehomeSelectOncall,
    What3WordsCleo,
    CleoModal,
    LoadingSpinnerLarge,
    ConsultsSection,
    PaccsForm,
    MountingPortal,
    CompleteForm,
  },
  props: {
    callDetail: {
      type: Object as PropType<ICallDetail>,
      default: () => {
        return CallDetailService.factoryCallDetail();
      },
    },
    userPermissions: {
      type: Object as PropType<CleoPermissionsForRole>,
      default: () => {
        return {};
      },
    },
    isNewCall: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    startedConsult: {
      type: Boolean,
      default: () => {
        return true;
      },
    },
    caseConfig: {
      type: Object as PropType<ICaseConfig>,
      default: () => {
        return CallDetailService.factoryCaseConfig();
      },
    },
  },
  setup(
    props: {
      callDetail: ICallDetail;
      userPermissions: Record<string, ICleoPermission>;
      isNewCall: boolean;
      startedConsult: boolean;
      caseConfig: ICaseConfig;
    },
    context: SetupContext
  ) {
    const store = appStore;

    const keyWordStoreState = computed<IKeywordsStoreState>(() => {
      return store.state[
        KEYWORD_STORE_STORE_CONST.KEYWORD_STORE__CONST_MODULE_NAME
      ];
    });

    const getClassifications = computed(() => {
      // Adapter falling over...
      const classifications =
        keyWordStoreState.value.keywordsRefDataKey.classifications;
      if (classifications && Object.keys(classifications).length > 0) {
        return classifications;
      } else {
        return {
          Advice: {
            Id: 1,
            Description: "Advice",
          },
          Base: {
            Id: 2,
            Description: "Base",
          },
          Visit: {
            Id: 3,
            Description: "Visit",
          },
        };
      }
    });

    const what3wordsControllerState: IWhat3wordsControllerState = new What3wordsCleoService().factoryWhat3wordsControllerState();
    what3wordsControllerState.userInput.userPermissions = props.userPermissions;

    const callDetailController = useCallDetailController();
    callDetailController.state.callDetail = commonService.simpleObjectClone(
      props.callDetail
    );

    const paccsStoreState = computed<IPaccsStoreState>(() => {
      return store.state[PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME];
    });

    const configStoreState = computed<IConfigStoreState>(() => {
      return store.state[CONFIG_STORE_CONST.CONFIG__CONST_MODULE_NAME];
    });

    const permissionStoreState = computed<IPermissionStoreState>(() => {
      return store.state[PERMISSION_STORE_CONST.PERMISSION__CONST_MODULE_NAME];
    });

    const showPaccs = computed<boolean>(() => {
      return (
        hasPermissionPaccs.value && callDetailController.state.startedConsult
      );
    });

    const isPaccsReady = computed<boolean>(() => {
      return paccsStoreState.value.addCaseRecordResponse.caseId.length > 0;
    });

    // let state: ICallDetailState = reactive({
    //   ...CallDetailService.factoryICallDetailState(),
    //   callDetail: commonService.simpleObjectClone(props.callDetail)
    // });

    console.log("CallDetail.setup() props:", {
      props,
    });

    watch(
      () => props.callDetail,
      (newValue: ICallDetail, oldValue: ICallDetail) => {
        callDetailController.state.callDetail = commonService.simpleObjectClone(
          newValue
        );
      }
    );

    const onCallChanged = () => {
      context.emit("call-changed", callDetailController.state.callDetail);
    };

    const hasPermissionPaccs = computed<boolean>(() => {
      //  TODO move into CallController in this App
      return window.CallControllerClient.hasPermission("PACCS_USER");
    });

    const canShowFollowUp = computed<boolean>(() => {
      return (
        permissionStoreState.value.userAppPermsShort.CASE_MOVE_TO_OVERSIGHT &&
        callDetailController.state.startedConsult
      );
    });

    // const hasStartedConsult = computed<boolean>(() => {
    //   return callDetailController.state.startedConsult;
    // });

    watch(
      () => props.startedConsult,
      (newValue: boolean, oldValue: boolean) => {
        callDetailController.state.startedConsult = newValue;

        if (newValue) {
          store.dispatch(
            PACCS_STORE_CONST.PACCS__CONST_MODULE_NAME +
              "/" +
              PACCS_STORE_CONST.PACCS__ACTION_INIT_PACCS,
            {}
          );
        }
      }
    );

    /**
     * THis is the bridge between the legacy UI and the new UI.
     */
    watch(
      () => configStoreState.value.adapterCleoAction,
      (newValue: IAdapterAction, oldValue: IAdapterAction) => {
        loggerInstance.log("call-detail>>>>>>>>>>>>>>>>>>watch!", {
          newValue,
          oldValue,
        });
        if (newValue.payload.actionType === "SHOW_ADDRESS_MAP") {
          callDetailController.state.address.showModal = true;
        }

        if (newValue.payload.actionType === "SAVE_AND_RETURN") {
          callDetailController.launchSaveAndReturn();
        }
        if (newValue.payload.actionType === "END_ASSESSMENT") {
          callDetailController.launchEndAssessment();
        }
      }
    );

    onMounted(() => {
      let what3WordScript = document.createElement("script");
      //  TODO from config.
      what3WordScript.setAttribute(
        "src",
        "https://cdn.what3words.com/javascript-components@" +
          what3wordsControllerState.version +
          "/dist/what3words/what3words.js?key=" +
          what3wordsControllerState.apiKey
      );
      document.head.appendChild(what3WordScript);
      callDetailController.state.address.isWhat3WordsReady = true;

      callDetailController.mapLegacyUiToModel();
    });

    onBeforeUnmount(() => {
      callDetailController.destroy();
    });

    function what3wordsMapConfirmed(
      what3WordsMapResponse: IWhat3WordsMapResponse
    ) {
      callDetailController.state.address.showModal = false;
      window.CallControllerClient.userLookupController.what3WordsMapResponse(
        what3WordsMapResponse
      );
    }

    function cancelCompleteProcess(
      completeControllerState: ICompleteControllerState
    ) {
      callDetailController.cancelComplete(completeControllerState);
    }

    function processComplete(
      completeControllerState: ICompleteControllerState
    ) {
      callDetailController.processComplete(completeControllerState);
    }

    function saveAndReturnToQueue(
      completeControllerState: ICompleteControllerState
    ) {
      callDetailController.saveAndReturn(completeControllerState);
    }

    function furtherActionRequired(
      completeControllerState: ICompleteControllerState
    ) {
      callDetailController.saveAndReturn(completeControllerState);
    }

    return {
      store,

      callDetailController,
      hasPermissionPaccs,
      showPaccs,
      isPaccsReady,
      getClassifications,
      keyWordStoreState,
      canShowFollowUp,
      what3wordsControllerState,
      configStoreState,
      permissionStoreState,

      onCallChanged,
      what3wordsMapConfirmed,
      cancelCompleteProcess,
      processComplete,
      saveAndReturnToQueue,
      furtherActionRequired,
    };
  },
});
</script>
